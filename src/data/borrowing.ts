
interface StatusOption {
    label: string;
    value: number;
    type: 'default' | 'primary' | 'success' | 'error';
}
/**
 * 借阅与交还 状态
 */
const status: StatusOption[] = [
    {
        label: '系统处理中',
        value: -1,
        type: 'default'
    },
    {
        label: '待提交',
        value: 1,
        type: 'default'
    },
    {
        label: '待审批',
        value: 2,
        type: 'primary'
    },
    {
        label: '已审批',
        value: 3,
        type: 'success'
    },
    {
        label: '已驳回',
        value: 4,
        type: 'error'
    }
];

// 借阅原因选项
const reasonOptions = [
    { label: '项目参考/研究', value: 1 },
    { label: '问题调查/分析', value: 2 },
    { label: '审计/检查准备', value: 3 },
    { label: '培训/学习需要', value: 4 },
    { label: '其他', value: 5 }
];

// 状态选项
const documentValidityOptions = [
    { label: '作废', value: -1 },
    { label: '即将作废', value: 1 },
    { label: '即将实施', value: 2 },
    { label: '有效', value: 3 },
    { label: '拟修订', value: 4 }
];

// 类型选项
const typeOptions = [
    { label: '内部文件', value: 2 },
    { label: '外部文件', value: 3 }
];


export default {
    status,
    typeOptions,
    reasonOptions,
    documentValidityOptions
};
