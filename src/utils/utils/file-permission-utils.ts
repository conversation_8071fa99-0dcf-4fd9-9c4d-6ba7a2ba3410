// @/utils/file-permission.ts
import useStore from '@/store/modules/main';
import { NButton } from 'naive-ui';

/**
 * 文件权限校验和二次确认的工具函数
 *
 * 整体使用流程：
 * 1. 用户点击查阅/下载按钮
 * 2. 调用 handleView() 或 handleDownload() 方法
 * 3. 系统获取用户对该文件的所有权限记录
 * 4. 根据权限类型和状态进行权限校验
 * 5. 权限处理逻辑：有权限直接执行，无权限显示申请对话框
 * 6. 执行具体操作（查阅预览或下载申请）
 */

// ==================== 权限类型常量定义 ====================
const PERMISSION_TYPES = {
    VIEW: {
        ELECTRONIC_VIEW: { fileForm: 1, filePermission: 1 },
        ELECTRONIC_DOWNLOAD: { fileForm: 1, filePermission: 2 }
    },
    DOWNLOAD: {
        ELECTRONIC_DOWNLOAD: { fileForm: 1, filePermission: 2 },
        ELECTRONIC_ONCE: { fileForm: 1, filePermission: 3 },
        PAPER_ONCE: { fileForm: 2, filePermission: 3 }
    }
} as const;

const RECORD_STATUS = {
    PENDING: 1,
    APPROVED: 2,
    USED: 3,
    REJECTED: 4
} as const;

const OPERATION_TYPE = {
    VIEW: 1,
    DOWNLOAD: 2
} as const;

// ==================== 权限判断工具 ====================
class PermissionValidator {
    /**
     * 判断是否为有效的查阅权限
     */
    static isValidViewPermission(record: any): boolean {
        const { fileForm, filePermission, status } = record;
        const isValidType =
            (fileForm === PERMISSION_TYPES.VIEW.ELECTRONIC_VIEW.fileForm &&
                filePermission === PERMISSION_TYPES.VIEW.ELECTRONIC_VIEW.filePermission) ||
            (fileForm === PERMISSION_TYPES.VIEW.ELECTRONIC_DOWNLOAD.fileForm &&
                filePermission === PERMISSION_TYPES.VIEW.ELECTRONIC_DOWNLOAD.filePermission);
        return isValidType && status !== RECORD_STATUS.REJECTED;
    }

    /**
     * 判断是否为有效的下载权限
     */
    static isValidDownloadPermission(record: any): boolean {
        const { fileForm, filePermission, status, isUsed } = record;

        // 直接下载权限（fileForm=1, filePermission=2）
        if (
            fileForm === PERMISSION_TYPES.DOWNLOAD.ELECTRONIC_DOWNLOAD.fileForm &&
            filePermission === PERMISSION_TYPES.DOWNLOAD.ELECTRONIC_DOWNLOAD.filePermission
        ) {
            return status !== RECORD_STATUS.REJECTED;
        }

        // 一次下载权限（fileForm=1, filePermission=3 或 fileForm=2, filePermission=3）
        const isOncePermission =
            (fileForm === PERMISSION_TYPES.DOWNLOAD.ELECTRONIC_ONCE.fileForm &&
                filePermission === PERMISSION_TYPES.DOWNLOAD.ELECTRONIC_ONCE.filePermission) ||
            (fileForm === PERMISSION_TYPES.DOWNLOAD.PAPER_ONCE.fileForm &&
                filePermission === PERMISSION_TYPES.DOWNLOAD.PAPER_ONCE.filePermission);

        return isOncePermission && status > RECORD_STATUS.PENDING && isUsed !== true;
    }

    /**
     * 判断是否为下载相关的权限（用于检查进行中的申请）
     */
    static isDownloadRelatedPermission(record: any): boolean {
        const { fileForm, filePermission } = record;
        return (
            (fileForm === 1 && filePermission === 2) ||
            (fileForm === 1 && filePermission === 3) ||
            (fileForm === 2 && filePermission === 3)
        );
    }

    /**
     * 判断是否为进行中的申请
     */
    static isOngoingRecord(record: any): boolean {
        return record.status === RECORD_STATUS.PENDING;
    }

    /**
     * 判断是否为已使用的下载权限
     */
    static isUsedDownloadPermission(record: any): boolean {
        const { fileForm, filePermission, status, isUsed } = record;

        // 直接下载权限不判断 isUsed
        if (
            fileForm === PERMISSION_TYPES.DOWNLOAD.ELECTRONIC_DOWNLOAD.fileForm &&
            filePermission === PERMISSION_TYPES.DOWNLOAD.ELECTRONIC_DOWNLOAD.filePermission
        ) {
            return status !== RECORD_STATUS.APPROVED && status !== RECORD_STATUS.USED;
        }

        // 一次下载权限判断 isUsed
        const isOncePermission =
            (fileForm === PERMISSION_TYPES.DOWNLOAD.ELECTRONIC_ONCE.fileForm &&
                filePermission === PERMISSION_TYPES.DOWNLOAD.ELECTRONIC_ONCE.filePermission) ||
            (fileForm === PERMISSION_TYPES.DOWNLOAD.PAPER_ONCE.fileForm &&
                filePermission === PERMISSION_TYPES.DOWNLOAD.PAPER_ONCE.filePermission);

        return isOncePermission && isUsed === true;
    }

    /**
     * 获取权限显示名称
     */
    static getPermissionDisplayName(fileForm: number, filePermission: number): string {
        if (fileForm === 1 && filePermission === 2) return '下载';
        if (fileForm === 1 && filePermission === 3) return '电子文件：一次下载';
        if (fileForm === 2 && filePermission === 3) return '纸质文件：一次下载';
        return '未知权限';
    }
}

// ==================== 核心服务 ====================
class FilePermissionUtils {
    /**
     * 获取store实例（懒加载）
     */
    private static getStore() {
        return useStore();
    }

    /**
     * 文件预览
     */
    static async preview(file: { id: string, name: string }) {
        return window.$dialog.success({
            title: '请选择预览方式',
            autoFocus: false,
            style: `width: 300px;`,
            content: () =>
                h('div', { style: 'display: flex; flex-direction: column; gap: 12px; justify-content: center; padding: 10px 5px;' }, [
                    h(NButton, {
                        onClick: () => {
                            window.$dialog.destroyAll();
                            nextTick(() => {
                                $alert.dialog({
                                    title: `文件预览: ${file.name}`,
                                    width: '80%',
                                    content: import('@/components/file-preview.vue'),
                                    props: {
                                        id: file.id,
                                        name: file.name,
                                        format: 'pdf'
                                    }
                                });
                            })

                        }
                    }, { default: () => '直接预览' }),
                    h(NButton, {
                        onClick: () => {
                            window.$dialog.destroyAll();
                            nextTick(() => {
                                $alert.dialog({
                                    title: '扫码预览',
                                    content: import('@/views/hierarchy/file-management/models/scan-code-preview.vue'),
                                    props: {
                                        file
                                    }
                                });
                            })
                        }
                    }, { default: () => '扫码预览' })
                ]),
        });
    }

    /**
     * 检查当前用户是否为管理员
     */
    static async checkIsAdmin(): Promise<{ isAdmin: boolean }> {
        try {
            const store = this.getStore();
            const res = await window.api.sass.api.v1.genericInterface.getUserRoles(store.userInfo.id);
            const isAdmin = res.data.includes('JTWJGLY') || res.data.includes('ZGSWJGLY');
            return { isAdmin: isAdmin };
        } catch (error) {
            console.error('检查管理员权限失败:', error);
            return { isAdmin: false };
        }
    }

    /**
     * 获取用户对指定文件的权限记录
     */
    private static async getUserPermissions(row: any) {
        const store = this.getStore();
        const res = await window.$apis.nebula.api.v1.businessDictionary.node.getRecord({
            documentId: row.id,
            userId: store.userInfo.id
        });
        return res.data.list || [];
    }

    /**
     * 执行文件操作
     */
    private static async executeFileOperation(
        row: any,
        operationType: 1 | 2,
        fileForm: number,
        filePermission: number,
        inventoryId?: string
    ): Promise<void> {
        const resData = await window.$apis.nebula.api.v1.businessDictionary.node.isPermission({
            documentId: row.id,
            fileForm: fileForm,
            inventoryId: inventoryId || '',
            filePermission: filePermission,
            operationType: operationType
        });

        if (operationType === OPERATION_TYPE.VIEW) {
            // 查阅操作
            if (resData.data.fileId || row.fileInfo?.fileId) {
                this.preview({
                    id: resData.data.fileId || row.fileInfo?.fileId,
                    name: row.name
                });
            } else {
                window.$message.error('文件不存在');
            }
        } else {
            // 下载操作
            if (resData.data.fileId) {
                window.$message.success('下载申请成功，请在数据导出中查看');
            } else {
                window.$message.error('下载失败，没有文件');
            }
        }
    }

    /**
     * 显示权限选择对话框
     */
    private static showPermissionSelectionDialog(
        permissions: Array<{ fileForm: number; filePermission: number; inventoryId: string }>,
        onConfirm: (fileForm: number, filePermission: number, inventoryId?: string) => void
    ) {
        const buttons = permissions.map((permission) =>
            h(
                NButton,
                {
                    size: 'medium',
                    style: 'min-width: 140px; margin: 0 8px;',
                    onClick: () => {
                        window.$dialog.destroyAll();
                        onConfirm(permission.fileForm, permission.filePermission, permission.inventoryId);
                    }
                },
                () => PermissionValidator.getPermissionDisplayName(permission.fileForm, permission.filePermission)
            )
        );

        window.$dialog.warning({
            title: '请选择您希望使用的下载方式',
            autoFocus: false,
            style: `width: ${Math.max(400, buttons.length * 160)}px;`,
            content: () =>
                h('div', { style: 'display: flex; flex-direction: column; gap: 12px; justify-content: center; padding: 10px 5px;' }, buttons),
        });
    }

    /**
     * 显示权限申请对话框
     */
    private static showPermissionRequestDialog(row: any, operationType: 'view' | 'download', fileType: number) {
        window.$dialog.error({
            title: operationType === 'view' ? '无查阅权限' : '无下载权限',
            autoFocus: false,
            content:
                operationType === 'view'
                    ? '您当前没有查阅该文件的权限，是否发起查阅申请？'
                    : '您当前没有下载该文件的权限，是否发起下载申请？',
            positiveText: '发起申请',
            onPositiveClick: () => {
                $alert.dialog({
                    title: operationType === 'view' ? '查阅申请' : '下载申请',
                    width: '60%',
                    content: import('@/views/hierarchy/file-management/models/refer-to-from.vue'),
                    props: {
                        row,
                        type: operationType === 'view' ? 1 : 2,
                        fileType: fileType
                    }
                });
            }
        });
    }

    /**
     * 处理查阅操作
     */
    static async handleView(row: any, fileType: number): Promise<void> {
        try {
            const allRecords = await this.getUserPermissions(row);

            // 过滤有效权限和进行中的申请
            const validRecords = allRecords.filter(PermissionValidator.isValidViewPermission);
            const ongoingRecords = allRecords.filter(PermissionValidator.isOngoingRecord);

            if (validRecords.length > 0) {
                // 有权限，优先选择状态为 2 或 3 的权限
                const bestPermission =
                    validRecords.find(
                        (record: any) =>
                            record.status === RECORD_STATUS.APPROVED || record.status === RECORD_STATUS.USED
                    ) || validRecords[0];

                await this.executeFileOperation(
                    row,
                    OPERATION_TYPE.VIEW,
                    bestPermission.fileForm,
                    bestPermission.filePermission,
                    bestPermission.inventoryId
                );
            } else {
                // 检查是否有进行中的查阅申请
                const ongoingViewRecords = ongoingRecords.filter(
                    (record: any) =>
                        (record.fileForm === 1 && record.filePermission === 1) ||
                        (record.fileForm === 1 && record.filePermission === 2)
                );

                if (ongoingViewRecords.length > 0) {
                    window.$message.warning('当前已有申请流程，请在流程完成后重试');
                } else {
                    this.showPermissionRequestDialog(row, 'view', fileType);
                }
            }
        } catch (error) {
            console.error('查阅操作处理失败:', error);
        }
    }

    /**
     * 处理下载操作
     */
    static async handleDownload(row: any, fileType: number): Promise<void> {
        try {
            const allRecords = await this.getUserPermissions(row);

            // 过滤有效权限、进行中的申请和已使用的权限
            const validRecords = allRecords.filter(PermissionValidator.isValidDownloadPermission);
            const ongoingRecords = allRecords.filter(PermissionValidator.isOngoingRecord);
            const usedRecords = allRecords.filter(PermissionValidator.isUsedDownloadPermission);

            // 去重收集可用权限
            const downloadPermissions = validRecords.reduce(
                (acc: Array<{ fileForm: number; filePermission: number; inventoryId: string }>, record: any) => {
                    const key = `${record.fileForm}-${record.filePermission}`;
                    if (!acc.some((p: any) => `${p.fileForm}-${p.filePermission}` === key)) {
                        acc.push({
                            fileForm: record.fileForm,
                            filePermission: record.filePermission,
                            inventoryId: record.inventoryId
                        });
                    }
                    return acc;
                },
                [] as Array<{ fileForm: number; filePermission: number; inventoryId: string }>
            );

            // 1. 有可用下载权限
            if (downloadPermissions.length > 0) {
                if (downloadPermissions.length === 1) {
                    // 只有一个权限，直接执行
                    const permission = downloadPermissions[0];
                    await this.executeFileOperation(
                        row,
                        OPERATION_TYPE.DOWNLOAD,
                        permission.fileForm,
                        permission.filePermission,
                        permission.inventoryId
                    );
                } else {
                    // 多个权限，让用户选择
                    this.showPermissionSelectionDialog(
                        downloadPermissions,
                        async (fileForm, filePermission, inventoryId) => {
                            await this.executeFileOperation(
                                row,
                                OPERATION_TYPE.DOWNLOAD,
                                fileForm,
                                filePermission,
                                inventoryId
                            );
                        }
                    );
                }
                return;
            }

            // 2. 有进行中的下载申请
            const ongoingDownloadRecords = ongoingRecords.filter(PermissionValidator.isDownloadRelatedPermission);
            if (ongoingDownloadRecords.length > 0) {
                window.$message.warning('当前已有申请流程，请在流程完成后重试');
                return;
            }

            // 3. 有已使用的下载权限
            if (usedRecords.length > 0) {
                const adminCheck = await this.checkIsAdmin();

                // 检查普通用户是否已经有电子文件一次下载权限
                if (!adminCheck.isAdmin) {
                    const hasElectronicOncePermission = usedRecords.some(
                        (record: any) => record.fileForm === 1 && record.filePermission === 3
                    );

                    if (hasElectronicOncePermission) {
                        window.$message.warning('您已经下载过该文件，无法再次申请下载');
                        return;
                    }
                }

                // 显示已使用权限信息，询问是否申请新权限
                const usedPermissionsText = usedRecords
                    .map((record: any) =>
                        PermissionValidator.getPermissionDisplayName(record.fileForm, record.filePermission)
                    )
                    .join('、');

                window.$dialog.warning({
                    title: '权限已使用',
                    autoFocus: false,
                    content: `您已经使用过以下下载权限：\n${usedPermissionsText}\n\n是否要申请新的下载权限？`,
                    positiveText: '申请新权限',
                    negativeText: '取消',
                    onPositiveClick: () => {
                        // 直接打开发放申请弹框，而不是显示无权限对话框
                        $alert.dialog({
                            title: '发放申请',
                            width: '60%',
                            content: import('@/views/hierarchy/file-management/models/refer-to-from.vue'),
                            props: {
                                row,
                                type: 2,
                                fileType: fileType
                            }
                        });
                    }
                });
                return;
            }

            // 4. 都没有，弹出申请窗口
            this.showPermissionRequestDialog(row, 'download', fileType);
        } catch (error) {
            console.error('下载操作处理失败:', error);
            window.$message.error('文件下载失败');
        }
    }

    /**
     * 处理借阅操作
     * @param row 当前行数据
     * @param type 2-内部 3-外部 4-内部作废 5-外部作废
     */
    static async handleBorrow(row: any, type: number) {
        try {
            // 先查询是否有借阅权限
            const res = await $apis.nebula.api.v1.documentLibrary.loans.checkPermission({
                documentId: row.id,
                documentVersionNo: row.versionNo || row.version
            });
            // 1-可以借阅，2-有正在审批的流程，3-无借阅权限  4-有借阅权限但是不在借阅时间段
            switch (res.data.permission) {
                case 1:
                    // 借阅权限已存在,直接预览文件
                    this.preview({
                        id: res.data.fileId,
                        name: row.name
                    });
                    break;
                case 2:
                    // 已存在借阅申请
                    window.$dialog.warning({
                        title: '提示',
                        autoFocus: false,
                        content: `当前已有申请流程，请在流程完成后重试`,
                        positiveText: '确认'
                    });
                    break;
                case 3:
                    // 无借阅权限，询问是否申请借阅权限
                    window.$dialog.error({
                        title: '无借阅权限',
                        autoFocus: false,
                        content: '您当前没有借阅该文件的权限，是否发起借阅申请？',
                        positiveText: '发起申请',
                        negativeText: '取消',
                        onPositiveClick: () => {
                            // 确认后打开发放申请弹框
                            $alert.dialog({
                                title: '借阅申请',
                                width: '60%',
                                content: import(
                                    '@/views/hierarchy/file-management/borrowing-application/models/single-borrow-form.vue'
                                ),
                                props: {
                                    row,
                                    type
                                }
                            });
                        }
                    });
                    break;
                case 4:
                    // 不在借阅时间段
                    window.$dialog.warning({
                        title: '提示',
                        autoFocus: false,
                        content: `当前未在借阅日期内，请联系文件管理员`,
                        positiveText: '确认'
                    });
                    break;
            }
        } catch (error) {
            console.error('借阅操作处理失败:', error);
        }
    }
}

// ==================== 导出接口 ====================
export const handleView = (row: any, fileType: number): Promise<void> => {
    return FilePermissionUtils.handleView(row, fileType);
};

export const handleDownload = (row: any, fileType: number): Promise<void> => {
    return FilePermissionUtils.handleDownload(row, fileType);
};

export const checkIsAdmin = (): Promise<{ isAdmin: boolean }> => {
    return FilePermissionUtils.checkIsAdmin();
};

export const handleBorrow = (row: any, type: number): Promise<void> => {
    return FilePermissionUtils.handleBorrow(row, type);
};

export default {
    handleView,
    handleDownload,
    checkIsAdmin,
    handleBorrow
};
