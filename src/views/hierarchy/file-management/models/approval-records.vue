<template>
    <div class="approval-record">
        <div v-if="!approvalRecords || approvalRecords.length === 0" class="no-data text-center text-#999 py-20px">
            暂无审批记录
        </div>
        <div v-else class="approval-list mt-5px">
            <div v-for="(item, index) in approvalRecords" :key="index" class="approval-item py-4px">
                <div class="approval-row flex items-center gap-10px">
                    <span class="text-14px text-#666">{{ item.roleLabel }}</span>
                    <span class="text-14px font-medium">{{ item.approverName }}</span>
                    <span class="text-14px text-#666 ml-20px">{{ item.opinionLabel }}</span>
                    <span class="text-14px">{{ item.opinion }}</span>
                    <span class="text-14px text-#666 ml-20px">{{ item.dateLabel }}</span>
                    <span class="text-14px">{{ item.date }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';

// 常量定义
const APPROVAL_STATUS = {
    PASSED: 'passed',
    REJECTED: 'rejected',
    IN_PROGRESS: 'underReview'
} as const;

const SIGNING_KIND = {
    OR: 'or',
    AND: 'and'
} as const;

const ROLE_LABELS = {
    REVIEWER: '审核人',
    APPROVER: '批准人'
} as const;

const OPINION_LABELS = {
    REVIEW: '审核意见'
} as const;

const DATE_LABELS = {
    REVIEW: '审核日期',
    APPROVE: '批准日期'
} as const;

const OPINIONS = {
    AGREE: '同意',
    REJECT: '驳回',
    IN_PROGRESS: '进行中'
} as const;

interface ApprovalRecord {
    roleLabel: string;
    approverName: string;
    opinionLabel: string;
    opinion: string;
    dateLabel: string;
    date: string;
}

interface Approver {
    approverNickname: string;
    status: string;
    updatedAt: number;
}

interface ApprovalNode {
    approvers: Approver[];
    signingKind: string;
    status: string;
}

const props = withDefaults(
    defineProps<{
        flowId?: string;
        row?: any;
    }>(),
    {
        flowId: '',
        row: null
    }
);

const approvalRecords = ref<ApprovalRecord[]>([]);

// 工具函数
const formatDate = (timestamp: number | string): string => {
    if (!timestamp) return '--';
    return dayjs(timestamp).format('YYYY-MM-DD');
};

const getOpinionByStatus = (status: string): string => {
    switch (status) {
        case APPROVAL_STATUS.PASSED:
            return OPINIONS.AGREE;
        case APPROVAL_STATUS.REJECTED:
            return OPINIONS.REJECT;
        default:
            return OPINIONS.IN_PROGRESS;
    }
};

const createApprovalRecord = (
    roleLabel: string,
    approverName: string,
    opinion: string,
    dateLabel: string,
    date: string
): ApprovalRecord => ({
    roleLabel,
    approverName,
    opinionLabel: OPINION_LABELS.REVIEW,
    opinion,
    dateLabel,
    date
});

// 处理或签节点
const processOrSigningNode = (
    node: ApprovalNode,
    nodeCount: number,
    isLast: boolean,
    records: ApprovalRecord[]
): void => {
    const approverNames = node.approvers.map((approver) => approver.approverNickname || '').join('、');
    const passedApprover = node.approvers.find((approver) => approver.status === APPROVAL_STATUS.PASSED);
    const rejectedApprover = node.approvers.find((approver) => approver.status === APPROVAL_STATUS.REJECTED);

    let opinion: string = OPINIONS.IN_PROGRESS;
    let updateTime = 0;

    if (passedApprover) {
        opinion = OPINIONS.AGREE;
        updateTime = passedApprover.updatedAt;
    } else if (rejectedApprover) {
        opinion = OPINIONS.REJECT;
        updateTime = rejectedApprover.updatedAt;
    }

    const nodeInProgress = node.approvers.some(
        (approver) => approver.status !== APPROVAL_STATUS.PASSED && approver.status !== APPROVAL_STATUS.REJECTED
    );

    if (nodeCount === 1) {
        // 单个流程：先添加审核人记录
        records.push(
            createApprovalRecord(
                ROLE_LABELS.REVIEWER,
                approverNames,
                opinion,
                DATE_LABELS.REVIEW,
                formatDate(updateTime)
            )
        );

        // 如果已完成，添加批准人记录
        if (!nodeInProgress) {
            records.push(
                createApprovalRecord(
                    ROLE_LABELS.APPROVER,
                    approverNames,
                    opinion,
                    DATE_LABELS.APPROVE,
                    formatDate(updateTime)
                )
            );
        }
    } else {
        // 多个流程：根据节点位置判断角色
        const roleLabel = isLast ? ROLE_LABELS.APPROVER : ROLE_LABELS.REVIEWER;
        const dateLabel = isLast ? DATE_LABELS.APPROVE : DATE_LABELS.REVIEW;

        records.push(createApprovalRecord(roleLabel, approverNames, opinion, dateLabel, formatDate(updateTime)));
    }
};

// 处理会签节点
const processAndSigningNode = (
    node: ApprovalNode,
    nodeCount: number,
    isLast: boolean,
    records: ApprovalRecord[]
): void => {
    // 对审批人进行排序：已同意的在前，其他状态在后
    const sortedApprovers = [...node.approvers].sort((a, b) => {
        // 已同意的排在前面
        if (a.status === APPROVAL_STATUS.PASSED && b.status !== APPROVAL_STATUS.PASSED) {
            return -1;
        }
        if (b.status === APPROVAL_STATUS.PASSED && a.status !== APPROVAL_STATUS.PASSED) {
            return 1;
        }
        // 已驳回的排在进行中之前
        if (
            a.status === APPROVAL_STATUS.REJECTED &&
            b.status !== APPROVAL_STATUS.REJECTED &&
            b.status !== APPROVAL_STATUS.PASSED
        ) {
            return -1;
        }
        if (
            b.status === APPROVAL_STATUS.REJECTED &&
            a.status !== APPROVAL_STATUS.REJECTED &&
            a.status !== APPROVAL_STATUS.PASSED
        ) {
            return 1;
        }
        // 其他情况保持原顺序
        return 0;
    });

    sortedApprovers.forEach((approver, approverIndex) => {
        let roleLabel: string;
        let dateLabel: string;

        if (nodeCount === 1) {
            // 单个流程：第一个审批人是审核人，其他是批准人
            roleLabel = approverIndex === 0 ? ROLE_LABELS.REVIEWER : ROLE_LABELS.APPROVER;
            dateLabel = approverIndex === 0 ? DATE_LABELS.REVIEW : DATE_LABELS.APPROVE;
        } else {
            // 多个流程：根据是否是最后一个节点判断
            roleLabel = isLast ? ROLE_LABELS.APPROVER : ROLE_LABELS.REVIEWER;
            dateLabel = isLast ? DATE_LABELS.APPROVE : DATE_LABELS.REVIEW;
        }

        const opinion = getOpinionByStatus(approver.status);

        records.push(
            createApprovalRecord(
                roleLabel,
                approver.approverNickname || '',
                opinion,
                dateLabel,
                formatDate(approver.updatedAt)
            )
        );
    });
};

// 处理审批记录数据
const processApprovalRecords = (flowData: any): void => {
    const records: ApprovalRecord[] = [];

    if (!flowData?.nodes) {
        approvalRecords.value = records;
        return;
    }

    const nodes: ApprovalNode[] = flowData.nodes || [];
    const nodeCount = nodes.length;

    nodes.forEach((node, index) => {
        const isLast = index === nodeCount - 1;

        if (node.approvers?.length > 0) {
            if (node.signingKind === SIGNING_KIND.OR) {
                processOrSigningNode(node, nodeCount, isLast, records);
            } else {
                processAndSigningNode(node, nodeCount, isLast, records);
            }
        }
    });

    approvalRecords.value = records;
};

// 从row数据处理审批记录
const processApprovalRecordsFromRow = (row: any): void => {
    const records: ApprovalRecord[] = [];

    if (!row?.distributeApprovalInfo) {
        approvalRecords.value = records;
        return;
    }

    const info = row.distributeApprovalInfo;
    const hasApprovers = info.approvers?.length > 0;
    const hasAuditors = info.auditors?.length > 0;

    try {
        if (hasApprovers && !hasAuditors) {
            // 只有审核人的情况：审核人既是审核人也是批准人
            info.approvers.forEach((approver: any) => {
                const opinion = approver.passedDate ? OPINIONS.AGREE : OPINIONS.IN_PROGRESS;
                const date = formatDate(approver.passedDate);

                // 添加审核人记录
                records.push(
                    createApprovalRecord(
                        ROLE_LABELS.REVIEWER,
                        approver.userNickname || '',
                        opinion,
                        DATE_LABELS.REVIEW,
                        date
                    )
                );

                // 添加批准人记录（同一个人）
                records.push(
                    createApprovalRecord(
                        ROLE_LABELS.APPROVER,
                        approver.userNickname || '',
                        opinion,
                        DATE_LABELS.APPROVE,
                        date
                    )
                );
            });
        } else {
            // 有审核人和批准人的情况
            if (hasApprovers) {
                info.approvers.forEach((approver: any) => {
                    const opinion = approver.passedDate ? OPINIONS.AGREE : OPINIONS.IN_PROGRESS;
                    records.push(
                        createApprovalRecord(
                            ROLE_LABELS.REVIEWER,
                            approver.userNickname || '',
                            opinion,
                            DATE_LABELS.REVIEW,
                            formatDate(approver.passedDate)
                        )
                    );
                });
            }

            if (hasAuditors) {
                info.auditors.forEach((auditor: any) => {
                    const opinion = auditor.passedDate ? OPINIONS.AGREE : OPINIONS.IN_PROGRESS;
                    records.push(
                        createApprovalRecord(
                            ROLE_LABELS.APPROVER,
                            auditor.userNickname || '',
                            opinion,
                            DATE_LABELS.APPROVE,
                            formatDate(auditor.passedDate)
                        )
                    );
                });
            }
        }
    } catch (error) {
        console.error('处理审批记录数据时出错:', error);
    }

    approvalRecords.value = records;
};

// 获取工作流详情
const getWorkflowDetail = async (id: string): Promise<void> => {
    try {
        if (!id) {
            console.warn('工作流ID为空');
            return;
        }

        const res = await window.api.sass.api.v1.workflow.workflow.detail(id);

        if (!res.data) {
            throw new Error('获取审批详情失败：响应数据为空');
        }

        // 处理审批记录数据
        processApprovalRecords(res.data);
    } catch (error) {
        console.error('获取工作流详情失败:', error);
        window.$message.error('获取审批详情失败');
        // 设置空数据，避免界面异常
        approvalRecords.value = [];
    }
};

// 初始化审批记录
const initApprovalRecords = async (): Promise<void> => {
    try {
        if (props.flowId) {
            // 通过API获取审批详情
            await getWorkflowDetail(props.flowId);
        } else if (props.row) {
            // 从row数据中获取审批信息
            processApprovalRecordsFromRow(props.row);
        }
    } catch (error) {
        console.error('初始化审批记录失败:', error);
    }
};

onMounted(() => {
    initApprovalRecords();
});

// 监听props变化
watch(
    () => [props.flowId, props.row],
    () => {
        initApprovalRecords();
    },
    { deep: true }
);
</script>

<style scoped lang="less">
.approval-record {
    .approval-row {
        white-space: nowrap;
    }

    .no-data {
        font-size: 14px;
        color: #999;
    }
}
</style>
